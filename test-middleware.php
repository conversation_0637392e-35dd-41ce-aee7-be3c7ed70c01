<?php

// Test simple pour vérifier si notre middleware fonctionne
require_once 'vendor/autoload.php';

// Simuler l'application du middleware
$middleware = new App\Http\Middleware\ConfigureUploadLimits();

echo "=== AVANT APPLICATION DU MIDDLEWARE ===\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";

// Simuler une requête
$request = new Illuminate\Http\Request();
$next = function($request) {
    echo "\n=== APRÈS APPLICATION DU MIDDLEWARE ===\n";
    echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
    echo "post_max_size: " . ini_get('post_max_size') . "\n";
    echo "memory_limit: " . ini_get('memory_limit') . "\n";
    echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
    
    return new Illuminate\Http\Response('OK');
};

try {
    $response = $middleware->handle($request, $next);
    echo "\nMiddleware appliqué avec succès!\n";
} catch (Exception $e) {
    echo "\nErreur lors de l'application du middleware: " . $e->getMessage() . "\n";
}
?>
