
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\ServiceController;
use App\Http\Controllers\Api\V1\ToolController;
use App\Http\Controllers\Api\V1\TemplateController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API Version 1
Route::prefix('v1')->group(function () {
    
    // Authentication & Verification Routes (Public)
    Route::prefix('auth')->group(function () {
        Route::post('register', [AuthController::class, 'register']);
        Route::post('login', [AuthController::class, 'login']);
        Route::post('send-code', [AuthController::class, 'sendCode']);
        Route::post('verify-code', [AuthController::class, 'verifyCode']);
        
        // Protected auth routes
        Route::middleware('auth:sanctum')->group(function () {
            Route::get('me', [AuthController::class, 'me']);
            Route::post('logout', [AuthController::class, 'logout']);
        });
    });

    // Admin-specific routes
    Route::prefix('admin')->middleware(['auth:sanctum', 'throttle:api'])->group(function () {
        Route::get('collaborators', [UserController::class, 'collaborateurs']);
    });

    // Protected routes
    Route::middleware(['auth:sanctum', 'throttle:api'])->group(function () {
        
        // Users & Roles (Admin Only)
        Route::apiResource('users', UserController::class);
        
        // Collaborateurs Management (Admin Only)
        Route::prefix('collaborateurs')->group(function () {
            Route::get('/', [UserController::class, 'collaborateurs']);
            Route::post('/', [UserController::class, 'createCollaborateur']);
            Route::put('{id}', [UserController::class, 'update']);
            Route::delete('{id}', [UserController::class, 'destroy']);
        });

        // Services & Tools
        Route::apiResource('services', ServiceController::class);
        Route::prefix('services/{service}')->group(function () {
            Route::get('tools', [ToolController::class, 'index']);
            Route::post('tools', [ToolController::class, 'store']);
        });
        Route::apiResource('tools', ToolController::class)->except(['index', 'store']);

        // Templates (Files + Demo Video)
        Route::apiResource('templates', TemplateController::class);
        Route::get('templates/{template}/download', [TemplateController::class, 'download']);
    });
});

// Legacy route for backward compatibility
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
