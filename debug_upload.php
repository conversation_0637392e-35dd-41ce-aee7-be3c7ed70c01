<?php

echo "=== DEBUG UPLOAD CONFIGURATION ===\n";

// Vérifier la configuration PHP
echo "PHP Upload Configuration:\n";
echo "file_uploads: " . (ini_get('file_uploads') ? 'On' : 'Off') . "\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "max_input_time: " . ini_get('max_input_time') . "\n";

echo "\n=== DIRECTORY PERMISSIONS ===\n";

$directories = [
    'storage',
    'storage/app',
    'storage/app/public',
    'storage/app/public/templates',
    'storage/app/public/templates/files',
    'storage/app/public/templates/videos'
];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        $perms = substr(sprintf('%o', fileperms($dir)), -4);
        $writable = is_writable($dir) ? 'YES' : 'NO';
        echo "$dir: $perms (writable: $writable)\n";
    } else {
        echo "$dir: DOES NOT EXIST\n";
    }
}

echo "\n=== LARAVEL STORAGE LINK ===\n";
$publicStorageLink = 'public/storage';
if (is_link($publicStorageLink)) {
    echo "Storage link exists: YES\n";
    echo "Link target: " . readlink($publicStorageLink) . "\n";
} else {
    echo "Storage link exists: NO\n";
}

echo "\n=== DISK SPACE ===\n";
$freeBytes = disk_free_space('.');
$totalBytes = disk_total_space('.');
echo "Free space: " . round($freeBytes / 1024 / 1024, 2) . " MB\n";
echo "Total space: " . round($totalBytes / 1024 / 1024, 2) . " MB\n";

echo "\n=== TEMP DIRECTORY ===\n";
$tempDir = sys_get_temp_dir();
echo "Temp directory: $tempDir\n";
echo "Temp dir writable: " . (is_writable($tempDir) ? 'YES' : 'NO') . "\n";

?>
