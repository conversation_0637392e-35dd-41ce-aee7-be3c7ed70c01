<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            'manage-users',
            'manage-services',
            'manage-tools',
            'manage-templates',
            'download-templates',
            'view-templates',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $collaborateurRole = Role::firstOrCreate(['name' => 'collaborateur']);
        $userRole = Role::firstOrCreate(['name' => 'user']);

        // Admin has all permissions
        $adminRole->givePermissionTo(Permission::all());

        // Collaborateur can download and view templates
        $collaborateurRole->givePermissionTo([
            'download-templates',
            'view-templates',
        ]);

        // User can only view templates
        $userRole->givePermissionTo([
            'view-templates',
        ]);

        // Create default admin user
        $admin = User::firstOrCreate(
            ['phone' => '+1234567890'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('admin123'),
                'is_verified' => true,
                'phone_verified_at' => now(),
            ]
        );

        $admin->assignRole('admin');

        $this->command->info('Roles and permissions seeded successfully!');
        $this->command->info('Default admin created:');
        $this->command->info('Phone: +1234567890');
        $this->command->info('Password: admin123');
    }
}

