<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Upload Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the upload configuration for the application.
    | These settings define the maximum file sizes and upload limits.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Maximum PowerPoint File Size
    |--------------------------------------------------------------------------
    |
    | The maximum PowerPoint file size allowed for uploads in kilobytes.
    | 15360 KB = 15 MB (suitable for ~30 slides)
    |
    */

    'max_powerpoint_size' => env('UPLOAD_MAX_POWERPOINT_SIZE', 15360), // 15MB in KB

    /*
    |--------------------------------------------------------------------------
    | Maximum Video Size
    |--------------------------------------------------------------------------
    |
    | The maximum video file size allowed for uploads in kilobytes.
    | 102400 KB = 100 MB (suitable for 2-minute demo videos)
    |
    */

    'max_video_size' => env('UPLOAD_MAX_VIDEO_SIZE', 102400), // 100MB in KB

    /*
    |--------------------------------------------------------------------------
    | Maximum Document File Size
    |--------------------------------------------------------------------------
    |
    | The maximum document file size allowed for uploads in kilobytes.
    | 51200 KB = 50 MB (for other document types)
    |
    */

    'max_document_size' => env('UPLOAD_MAX_DOCUMENT_SIZE', 51200), // 50MB in KB

    /*
    |--------------------------------------------------------------------------
    | Allowed File Types
    |--------------------------------------------------------------------------
    |
    | The allowed file types for different upload categories.
    |
    */

    'allowed_types' => [
        'powerpoint' => [
            'mimes' => 'ppt,pptx,application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'extensions' => ['ppt', 'pptx'],
            'max_size' => 'max_powerpoint_size', // Reference to config key
        ],
        'documents' => [
            'mimes' => 'doc,docx,pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/pdf',
            'extensions' => ['doc', 'docx', 'pdf'],
            'max_size' => 'max_document_size', // Reference to config key
        ],
        'videos' => [
            'mimes' => 'mp4,avi,mov,webm,mkv,flv,wmv,video/mp4,video/avi,video/quicktime,video/webm,video/x-msvideo',
            'extensions' => ['mp4', 'avi', 'mov', 'webm', 'mkv', 'flv', 'wmv'],
            'max_size' => 'max_video_size', // Reference to config key
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | PHP Configuration Recommendations
    |--------------------------------------------------------------------------
    |
    | These are the recommended PHP configuration values for handling
    | large file uploads. These should be set in your php.ini or
    | web server configuration.
    |
    */

    'php_recommendations' => [
        'upload_max_filesize' => '100M', // Adjusted for video files
        'post_max_size' => '110M', // Slightly higher than upload_max_filesize
        'max_execution_time' => 600,
        'max_input_time' => 600,
        'memory_limit' => '512M',
        'max_file_uploads' => 20,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Type Validation Messages
    |--------------------------------------------------------------------------
    |
    | Custom error messages for different file types and size limits.
    |
    */

    'validation_messages' => [
        'powerpoint_size' => 'PowerPoint files must be smaller than 15MB. For presentations with ~30 slides, consider optimizing images and removing unused elements.',
        'video_size' => 'Demo videos must be smaller than 100MB. For 2-minute videos, consider using MP4 format with moderate compression.',
        'document_size' => 'Document files must be smaller than 50MB.',
        'invalid_powerpoint' => 'Only PowerPoint files (.ppt, .pptx) are allowed for presentations.',
        'invalid_video' => 'Only video files (MP4, AVI, MOV, WebM, MKV, FLV, WMV) are allowed for demo videos.',
        'invalid_document' => 'Only document files (DOC, DOCX, PDF) are allowed.',
    ],

];
