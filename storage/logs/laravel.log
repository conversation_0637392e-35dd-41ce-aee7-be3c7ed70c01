[2025-07-14 14:45:54] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select()
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection()
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables()
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#23 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'root'@'localhost' (using password: NO) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct()
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback()
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run()
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select()
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(53): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\StatusCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/StatusCommand.php(52): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\StatusCommand->handle()
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure()
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call()
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call()
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute()
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run()
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand()
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun()
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run()
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#32 {main}
"} 
[2025-07-14 14:48:32] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/Compilers/Compiler.php:67)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(908): Illuminate\\View\\ViewServiceProvider->{closure:Illuminate\\View\\ViewServiceProvider::registerBladeCompiler():96}(Object(Illuminate\\Foundation\\Application), Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/ViewServiceProvider.php(168): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 [internal function]: Illuminate\\View\\ViewServiceProvider->{closure:Illuminate\\View\\ViewServiceProvider::registerBladeEngine():164}()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/Engines/EngineResolver.php(55): call_user_func(Object(Closure))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/Factory.php(310): Illuminate\\View\\Engines\\EngineResolver->resolve('blade')
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/Factory.php(274): Illuminate\\View\\Factory->getEngineFromPath('/home/<USER>/sa...')
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/Factory.php(146): Illuminate\\View\\Factory->viewInstance('welcome', '/home/<USER>/sa...', Array)
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(1020): Illuminate\\View\\Factory->make('welcome', Array, Array)
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/routes/web.php(17): view('welcome')
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure:/home/<USER>/sademys/sademy-api-source/sademy-api/routes/web.php:16}()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(208): Illuminate\\Routing\\Route->runCallable()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#46 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#48 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#52 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#54 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#58 {main}
"} 
[2025-07-14 14:48:32] local.ERROR: file_put_contents(/home/<USER>/sademys/sademy-api-source/sademy-api/storage/framework/sessions/QHu7E5s5rwGkmsbCPf9qcyjyO7uQy4W1ywAMBiKm): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): file_put_contents(/home/<USER>/sademys/sademy-api-source/sademy-api/storage/framework/sessions/QHu7E5s5rwGkmsbCPf9qcyjyO7uQy4W1ywAMBiKm): Failed to open stream: No such file or directory at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php:204)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(255): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'file_put_conten...', '/home/<USER>/sa...', 204)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->{closure:Illuminate\\Foundation\\Bootstrap\\HandleExceptions::forwardsTo():254}(2, 'file_put_conten...', '/home/<USER>/sa...', 204)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(204): file_put_contents('/home/<USER>/sa...', 'a:3:{s:6:\"_toke...', 2)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/FileSessionHandler.php(90): Illuminate\\Filesystem\\Filesystem->put('/home/<USER>/sa...', 'a:3:{s:6:\"_toke...', true)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/Store.php(172): Illuminate\\Session\\FileSessionHandler->write('QHu7E5s5rwGkmsb...', 'a:3:{s:6:\"_toke...')
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(245): Illuminate\\Session\\Store->save()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(130): Illuminate\\Session\\Middleware\\StartSession->saveSession(Object(Illuminate\\Http\\Request))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#39 {main}
"} 
[2025-07-14 23:24:11] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'sademy_user'@'localhost' (using password: YES) (Connection: mysql, SQL: select count(*) as aggregate from `users` where `phone` = +1234567890) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'sademy_user'@'localhost' (using password: YES) (Connection: mysql, SQL: select count(*) as aggregate from `users` where `phone` = +1234567890) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2901}()
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php(948): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('users', 'phone', '+1234567890', NULL, NULL, Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(660): Illuminate\\Validation\\Validator->validateUnique('phone', '+1234567890', Array, Object(Illuminate\\Validation\\Validator))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('phone', 'Unique')
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(494): Illuminate\\Validation\\Validator->passes()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1302): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->{closure:Illuminate\\Foundation\\Providers\\FormRequestServiceProvider::boot():29}(Object(App\\Http\\Requests\\RegisterRequest), Object(Illuminate\\Foundation\\Application))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1266): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Http\\Requests\\RegisterRequest), Array)
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1252): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(813): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Reques...', Array, true)
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Reques...', Array)
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Reques...', Array)
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(85): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Reques...')
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(50): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(29): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#44 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#45 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#48 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#63 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'sademy_user'@'localhost' (using password: YES) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=127....', 'sademy_user', Object(SensitiveParameterValue), Array)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'sademy_user', 'sademy_password', Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select count(*)...', Array)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2901}()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php(948): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('users', 'phone', '+1234567890', NULL, NULL, Array)
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(660): Illuminate\\Validation\\Validator->validateUnique('phone', '+1234567890', Array, Object(Illuminate\\Validation\\Validator))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('phone', 'Unique')
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(494): Illuminate\\Validation\\Validator->passes()
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1302): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->{closure:Illuminate\\Foundation\\Providers\\FormRequestServiceProvider::boot():29}(Object(App\\Http\\Requests\\RegisterRequest), Object(Illuminate\\Foundation\\Application))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1266): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Http\\Requests\\RegisterRequest), Array)
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1252): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(813): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Reques...', Array, true)
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Reques...', Array)
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Reques...', Array)
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(85): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Reques...')
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(50): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(29): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#43 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#44 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#45 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#46 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#47 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#53 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#54 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#60 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#62 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#64 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#66 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#68 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#72 {main}
"} 
[2025-07-14 23:29:16] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'sademy_user'@'localhost' (using password: YES) (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'sademy_user'@'localhost' (using password: YES) (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'sademy_user'@'localhost' (using password: YES) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=127....', 'sademy_user', Object(SensitiveParameterValue), Array)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'sademy_user', 'sademy_password', Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select table_na...', Array)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-07-14 23:34:27] local.ERROR: SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No such file or directory at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=loca...', 'sademy_user', Object(SensitiveParameterValue), Array)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'sademy_user', 'sademy123', Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=loca...', 'sademy_user', 'sademy123', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select table_na...', Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}
"} 
[2025-07-14 23:34:41] local.ERROR: SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(147): Illuminate\\Database\\Schema\\MySqlBuilder->getTables()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/WipeCommand.php(71): Illuminate\\Database\\Schema\\MySqlBuilder->dropAllTables()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/WipeCommand.php(48): Illuminate\\Database\\Console\\WipeCommand->dropAllTables(NULL)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\WipeCommand->handle()
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(40): Illuminate\\Console\\Command->runCommand('db:wipe', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(44): Illuminate\\Console\\Command->callSilent('db:wipe', Array)
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Database\\Console\\Migrations\\FreshCommand->{closure:Illuminate\\Database\\Console\\Migrations\\FreshCommand::handle():44}()
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render('Dropping all ta...', Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(44): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No such file or directory at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=loca...', 'sademy_user', Object(SensitiveParameterValue), Array)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'sademy_user', 'sademy123', Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=loca...', 'sademy_user', 'sademy123', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select table_na...', Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(147): Illuminate\\Database\\Schema\\MySqlBuilder->getTables()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/WipeCommand.php(71): Illuminate\\Database\\Schema\\MySqlBuilder->dropAllTables()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/WipeCommand.php(48): Illuminate\\Database\\Console\\WipeCommand->dropAllTables(NULL)
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\WipeCommand->handle()
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Symfony\\Component\\Console\\Output\\NullOutput))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Concerns/CallsCommands.php(40): Illuminate\\Console\\Command->runCommand('db:wipe', Array, Object(Symfony\\Component\\Console\\Output\\NullOutput))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(44): Illuminate\\Console\\Command->callSilent('db:wipe', Array)
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(37): Illuminate\\Database\\Console\\Migrations\\FreshCommand->{closure:Illuminate\\Database\\Console\\Migrations\\FreshCommand::handle():44}()
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/View/Components/Factory.php(58): Illuminate\\Console\\View\\Components\\Task->render('Dropping all ta...', Object(Closure))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/FreshCommand.php(44): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 {main}
"} 
[2025-07-14 23:35:24] local.ERROR: SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No such file or directory (Connection: mysql, SQL: select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'sademy_api' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No such file or directory at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:65)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(65): PDO->__construct('mysql:host=loca...', 'sademy_user', Object(SensitiveParameterValue), Array)
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(83): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=loca...', 'sademy_user', 'sademy123', Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=loca...', 'sademy_user', 'sademy123', Array)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=loca...', Array, Array)
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->{closure:Illuminate\\Database\\Connectors\\ConnectionFactory::createPdoResolverWithHosts():179}()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1339): call_user_func(Object(Closure))
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(528): Illuminate\\Database\\Connection->getPdo()
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): Illuminate\\Database\\Connection->getPdoForSelect(false)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select table_na...', Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(983): Illuminate\\Database\\Connection->runQueryCallback('select table_na...', Array, Object(Closure))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(962): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(785): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select table_na...', Array, Object(Closure))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select table_na...', Array, Object(Closure))
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(401): Illuminate\\Database\\Connection->select('select table_na...', Array, false)
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/MySqlBuilder.php(41): Illuminate\\Database\\Connection->selectFromWriteConnection('select table_na...')
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(165): Illuminate\\Database\\Schema\\MySqlBuilder->getTables(false)
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/DatabaseMigrationRepository.php(184): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(727): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Support/helpers.php(261): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::repositoryExists():140}(1)
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(140): retry(1, Object(Closure), 0, Object(Closure))
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(84): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::handle():83}()
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Util.php(41): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Command/Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 {main}
"} 
[2025-07-14 23:38:31] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sademy_api.users' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `users` where `phone` = +1234567890) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sademy_api.users' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `users` where `phone` = +1234567890) at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:829)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2901}()
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php(948): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('users', 'phone', '+1234567890', NULL, NULL, Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(660): Illuminate\\Validation\\Validator->validateUnique('phone', '+1234567890', Array, Object(Illuminate\\Validation\\Validator))
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('phone', 'Unique')
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(494): Illuminate\\Validation\\Validator->passes()
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1302): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->{closure:Illuminate\\Foundation\\Providers\\FormRequestServiceProvider::boot():29}(Object(App\\Http\\Requests\\RegisterRequest), Object(Illuminate\\Foundation\\Application))
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1266): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Http\\Requests\\RegisterRequest), Array)
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1252): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(813): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Reques...', Array, true)
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Reques...', Array)
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Reques...', Array)
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(85): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Reques...')
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(50): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(29): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#44 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#45 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#48 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#51 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#63 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'sademy_api.users' doesn't exist at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php:423)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(423): PDO->prepare('select count(*)...')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(816): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():414}('select count(*)...', Array)
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, false)
#5 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3456): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():2901}()
#7 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php(54): Illuminate\\Database\\Query\\Builder->count()
#11 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php(948): Illuminate\\Validation\\DatabasePresenceVerifier->getCount('users', 'phone', '+1234567890', NULL, NULL, Array)
#12 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(660): Illuminate\\Validation\\Validator->validateUnique('phone', '+1234567890', Array, Object(Illuminate\\Validation\\Validator))
#13 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(459): Illuminate\\Validation\\Validator->validateAttribute('phone', 'Unique')
#14 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/Validator.php(494): Illuminate\\Validation\\Validator->passes()
#15 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Validation/ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#16 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Providers/FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#17 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1302): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->{closure:Illuminate\\Foundation\\Providers\\FormRequestServiceProvider::boot():29}(Object(App\\Http\\Requests\\RegisterRequest), Object(Illuminate\\Foundation\\Application))
#18 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1266): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Http\\Requests\\RegisterRequest), Array)
#19 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(1252): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#20 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(813): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Http\\\\Reques...', Object(App\\Http\\Requests\\RegisterRequest))
#21 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Reques...', Array, true)
#22 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Container/Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Reques...', Array)
#23 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Reques...', Array)
#24 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(85): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Reques...')
#25 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(50): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#26 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ResolvesRouteDependencies.php(29): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#27 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#28 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(40): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#29 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Api\\V1\\AuthController), 'register')
#30 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Route.php(205): Illuminate\\Routing\\Route->runController()
#31 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(806): Illuminate\\Routing\\Route->run()
#32 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():805}(Object(Illuminate\\Http\\Request))
#33 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#34 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#36 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#37 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Middleware/ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#38 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#39 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#40 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Routing/Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#46 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():142}(Object(Illuminate\\Http\\Request))
#47 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#50 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#53 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#55 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#57 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#59 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():158}:159}(Object(Illuminate\\Http\\Request))
#61 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 /home/<USER>/sademys/sademy-api-source/sademy-api/public/index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(16): require_once('/home/<USER>/sa...')
#65 {main}
"} 
[2025-07-14 23:39:24] local.ERROR: Command "run" is not defined.

Did you mean one of these?
    cache:prune-stale-tags
    model:prune
    queue:prune-batches
    queue:prune-failed
    sanctum:prune-expired
    schedule:run {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"run\" is not defined.

Did you mean one of these?
    cache:prune-stale-tags
    model:prune
    queue:prune-batches
    queue:prune-failed
    sanctum:prune-expired
    schedule:run at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find('run')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-14 23:39:29] local.ERROR: Command "run" is not defined.

Did you mean one of these?
    cache:prune-stale-tags
    model:prune
    queue:prune-batches
    queue:prune-failed
    sanctum:prune-expired
    schedule:run {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"run\" is not defined.

Did you mean one of these?
    cache:prune-stale-tags
    model:prune
    queue:prune-batches
    queue:prune-failed
    sanctum:prune-expired
    schedule:run at /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php:737)
[stacktrace]
#0 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(266): Symfony\\Component\\Console\\Application->find('run')
#1 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/symfony/console/Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 /home/<USER>/sademys/sademy-api-source/sademy-api/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 /home/<USER>/sademys/sademy-api-source/sademy-api/artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-07-15 13:02:08] local.INFO: Template creation attempt {"has_file":true,"file_info":{"original_name":"demension.pdf","size":8861,"mime_type":"application/pdf","is_valid":true,"error":0},"request_data":{"name":"syfjfj","description":"gfhfgfg","type":"pdf","is_active":"1"}} 
[2025-07-15 14:31:41] local.INFO: Template creation attempt {"has_file":true,"file_info":{"original_name":"file_example_PPT_250kB.ppt","size":248320,"mime_type":"application/vnd.ms-powerpoint","is_valid":true,"error":0},"request_data":{"name":"hfghfg","description":"fghfgg","type":"ppt","is_active":"1"}} 
[2025-07-15 15:05:43] local.INFO: Template creation attempt {"has_file":true,"file_info":{"original_name":"file_example_PPT_250kB.ppt","size":248320,"mime_type":"application/vnd.ms-powerpoint","is_valid":true,"error":0},"request_data":{"name":"ddddddddddd","description":"dddddddddddddd","type":"ppt","is_active":"1"}} 
[2025-07-15 22:15:02] local.INFO: Template creation attempt {"has_file":true,"file_info":{"original_name":"export_dd7f8587-b372-4b18-998a-adf1f4feff5d.pptx","size":36371,"mime_type":"application/vnd.openxmlformats-officedocument.presentationml.presentation","is_valid":true,"error":0},"request_data":{"name":"ffdfdfddf","description":"dfdfdfdfdf","type":"ppt","is_active":"1"}} 
[2025-07-15 22:32:01] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-15 22:32:01] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-15 22:32:04] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-15 22:32:04] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-15 22:32:04] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-15 22:32:04] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-15 22:32:09] local.INFO: PHP upload limits configured {"upload_max_filesize":"2M","post_max_size":"8M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:25:38] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:26:46] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:26:46] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:26:49] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:26:49] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:26:57] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:26:57] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:10] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:10] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:21] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:21] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:26] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:26] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:29] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:29] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:31] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:31] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:35] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:35] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:49] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:49] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:56] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:27:56] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:31:27] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:33:11] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:33:32] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
[2025-07-22 15:33:50] local.INFO: PHP upload limits configured {"upload_max_filesize":"100M","post_max_size":"110M","memory_limit":"1G","max_execution_time":"1800"} 
