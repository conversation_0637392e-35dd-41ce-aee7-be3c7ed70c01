<!DOCTYPE html>
<html>
<head>
    <title>Test Upload</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Test Upload de Fichier</h1>
    
    <form action="/api/v1/templates" method="POST" enctype="multipart/form-data">
        <div>
            <label for="name">Nom:</label>
            <input type="text" id="name" name="name" value="Test Template" required>
        </div>
        
        <div>
            <label for="description">Description:</label>
            <textarea id="description" name="description">Test description</textarea>
        </div>
        
        <div>
            <label for="type">Type:</label>
            <select id="type" name="type" required>
                <option value="pdf">PDF</option>
                <option value="docx">DOCX</option>
                <option value="ppt">PPT</option>
            </select>
        </div>
        
        <div>
            <label for="file">Fichier:</label>
            <input type="file" id="file" name="file" accept=".pdf,.doc,.docx,.ppt,.pptx" required>
        </div>
        
        <div>
            <label for="demo_video">Vidéo démo (optionnel):</label>
            <input type="file" id="demo_video" name="demo_video" accept=".mp4,.avi,.mov">
        </div>
        
        <div>
            <label for="is_active">Actif:</label>
            <input type="checkbox" id="is_active" name="is_active" value="1" checked>
        </div>
        
        <button type="submit">Envoyer</button>
    </form>

    <script>
        // Ajouter un token CSRF si nécessaire
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/api/v1/templates', {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                    // Ajouter le token d'authentification si nécessaire
                    // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data);
                alert('Response: ' + JSON.stringify(data, null, 2));
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error: ' + error.message);
            });
        });
    </script>
</body>
</html>
