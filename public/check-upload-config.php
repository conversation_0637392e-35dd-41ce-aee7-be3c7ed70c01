<?php

/**
 * Upload Configuration Checker
 * 
 * This script checks if the server is properly configured for 200MB uploads.
 */

header('Content-Type: application/json');

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

function convertToBytes($value) {
    $value = trim($value);
    $last = strtolower($value[strlen($value) - 1]);
    $value = (int) $value;

    switch ($last) {
        case 'g':
            $value *= 1024;
        case 'm':
            $value *= 1024;
        case 'k':
            $value *= 1024;
    }

    return $value;
}

function checkConfiguration() {
    $config = [
        'php_version' => phpversion(),
        'file_uploads' => ini_get('file_uploads') ? 'Enabled' : 'Disabled',
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'upload_max_filesize_bytes' => convertToBytes(ini_get('upload_max_filesize')),
        'post_max_size' => ini_get('post_max_size'),
        'post_max_size_bytes' => convertToBytes(ini_get('post_max_size')),
        'max_file_uploads' => ini_get('max_file_uploads'),
        'memory_limit' => ini_get('memory_limit'),
        'memory_limit_bytes' => convertToBytes(ini_get('memory_limit')),
        'max_execution_time' => ini_get('max_execution_time'),
        'max_input_time' => ini_get('max_input_time'),
    ];

    // Check if configuration meets 100MB requirements (optimized for PowerPoint + Video uploads)
    $required_bytes = 100 * 1024 * 1024; // 100MB in bytes
    
    $checks = [
        'file_uploads_enabled' => $config['file_uploads'] === 'Enabled',
        'upload_max_filesize_ok' => $config['upload_max_filesize_bytes'] >= $required_bytes,
        'post_max_size_ok' => $config['post_max_size_bytes'] >= $required_bytes,
        'memory_limit_ok' => $config['memory_limit_bytes'] >= (512 * 1024 * 1024), // 512MB
        'execution_time_ok' => $config['max_execution_time'] >= 600 || $config['max_execution_time'] == 0,
    ];

    $all_checks_passed = array_reduce($checks, function($carry, $item) {
        return $carry && $item;
    }, true);

    return [
        'status' => $all_checks_passed ? 'OK' : 'NEEDS_CONFIGURATION',
        'message' => $all_checks_passed ?
            'Server is properly configured for PowerPoint (15MB) and video (100MB) uploads' :
            'Server configuration needs adjustment for large file uploads',
        'configuration' => $config,
        'checks' => $checks,
        'recommendations' => [
            'upload_max_filesize' => '100M',
            'post_max_size' => '110M',
            'memory_limit' => '512M',
            'max_execution_time' => '600',
            'max_input_time' => '600',
        ],
        'formatted_sizes' => [
            'upload_max_filesize' => formatBytes($config['upload_max_filesize_bytes']),
            'post_max_size' => formatBytes($config['post_max_size_bytes']),
            'memory_limit' => formatBytes($config['memory_limit_bytes']),
        ]
    ];
}

// Output the configuration check
echo json_encode(checkConfiguration(), JSON_PRETTY_PRINT);
?>
