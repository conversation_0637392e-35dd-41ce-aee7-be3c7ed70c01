<?php
header('Content-Type: application/json');

echo json_encode([
    'php_config' => [
        'file_uploads' => ini_get('file_uploads') ? 'On' : 'Off',
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'max_file_uploads' => ini_get('max_file_uploads'),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'max_input_time' => ini_get('max_input_time'),
    ],
    'storage_link' => [
        'exists' => is_link('storage'),
        'target' => is_link('storage') ? readlink('storage') : null,
    ],
    'directories' => [
        'storage_writable' => is_writable('../storage'),
        'storage_app_public_writable' => is_writable('../storage/app/public'),
        'templates_files_writable' => is_writable('../storage/app/public/templates/files'),
    ]
], JSON_PRETTY_PRINT);
?>
