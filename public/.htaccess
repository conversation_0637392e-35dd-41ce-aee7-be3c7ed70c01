<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Increase upload limits for PowerPoint and video files
<IfModule mod_php.c>
    php_value upload_max_filesize 100M
    php_value post_max_size 110M
    php_value memory_limit 512M
    php_value max_file_uploads 20
    php_value max_execution_time 600
    php_value max_input_time 600
</IfModule>

# For PHP-FPM
<IfModule mod_fcgid.c>
    FcgidMaxRequestLen 220200960
</IfModule>
