# Configuration des Uploads - PowerPoint et Vidéos

Ce document explique comment configurer le serveur pour autoriser des uploads optimisés pour les présentations PowerPoint (~15MB) et les vidéos de démonstration (~100MB).

## Modifications Apportées

### 1. Configuration Laravel

- **Validation intelligente des fichiers** :
  - PowerPoint (.ppt/.pptx) : Maximum 15MB (optimisé pour ~30 slides)
  - Vidéos de démonstration : Maximum 100MB (optimisé pour vidéos de 2 minutes)
  - Documents (.doc/.docx/.pdf) : Maximum 50MB
- **Middleware personnalisé** : `ConfigureUploadLimits` configure PHP dynamiquement
- **Configuration centralisée** : Fichier `config/upload.php` avec paramètres spécialisés
- **Messages d'erreur personnalisés** : Messages spécifiques selon le type de fichier et la taille
- **Validation MIME type étendue** : Support de multiples formats vidéo (MP4, AVI, MOV, WebM, MKV, FLV, WMV)

### 2. Configuration PHP

Les fichiers suivants ont été créés/modifiés :

- `public/.htaccess` : Configuration Apache avec limites 200MB
- `public/.user.ini` : Configuration PHP pour PHP-FPM/CGI
- `.user.ini` : Configuration PHP pour le répertoire racine

### 3. Scripts de Vérification

- `public/check-upload-config.php` : Script pour vérifier la configuration actuelle
- `public/test-upload.php` : Script existant pour tester les uploads

## Configuration Serveur Requise

### Apache

Si vous utilisez Apache, assurez-vous que le module `mod_rewrite` est activé et que les fichiers `.htaccess` sont autorisés :

```apache
<Directory "/path/to/your/app">
    AllowOverride All
</Directory>
```

### Nginx

Pour Nginx, ajoutez ces directives dans votre configuration :

```nginx
server {
    # Augmenter la taille maximale du corps de la requête
    client_max_body_size 210M;
    
    # Augmenter les timeouts
    client_body_timeout 600s;
    client_header_timeout 600s;
    
    location ~ \.php$ {
        # Configuration PHP-FPM
        fastcgi_read_timeout 600s;
        fastcgi_send_timeout 600s;
    }
}
```

### PHP-FPM

Modifiez votre fichier `php.ini` ou créez un fichier de configuration spécifique :

```ini
; Uploads
file_uploads = On
upload_max_filesize = 200M
post_max_size = 210M
max_file_uploads = 20

; Execution
max_execution_time = 600
max_input_time = 600

; Memory
memory_limit = 512M

; Input
max_input_vars = 3000
```

### PHP-FPM Pool Configuration

Dans votre fichier de pool PHP-FPM (généralement `/etc/php/8.x/fpm/pool.d/www.conf`) :

```ini
; Augmenter les timeouts
request_terminate_timeout = 600s

; Augmenter les limites de mémoire si nécessaire
php_admin_value[memory_limit] = 512M
```

## Vérification de la Configuration

### 1. Via le Script de Vérification

Accédez à : `http://votre-domaine.com/check-upload-config.php`

### 2. Via la Ligne de Commande

```bash
php public/check-upload-config.php
```

### 3. Vérification Manuelle

```bash
php -i | grep -E "(upload_max_filesize|post_max_size|memory_limit|max_execution_time)"
```

## Redémarrage des Services

Après modification de la configuration, redémarrez les services :

```bash
# Apache
sudo systemctl restart apache2

# Nginx
sudo systemctl restart nginx

# PHP-FPM
sudo systemctl restart php8.0-fpm  # Ajustez la version selon votre installation
```

## Test des Uploads

1. Utilisez le fichier `public/upload-test.html` pour tester via l'interface web
2. Ou testez via l'API directement avec un fichier de test

## Dépannage

### Problème : Les limites ne sont pas appliquées

1. Vérifiez que les fichiers `.user.ini` sont dans les bons répertoires
2. Vérifiez que PHP lit bien ces fichiers : `php -i | grep "Loaded Configuration File"`
3. Redémarrez le serveur web et PHP-FPM

### Problème : Timeout pendant l'upload

1. Augmentez `max_execution_time` et `max_input_time`
2. Vérifiez les timeouts du serveur web (Apache/Nginx)
3. Vérifiez les timeouts de PHP-FPM

### Problème : Erreur de mémoire

1. Augmentez `memory_limit` à 512M ou plus
2. Vérifiez la mémoire disponible sur le serveur

## Sécurité

- Les fichiers sont stockés avec des noms UUID pour éviter les accès non autorisés
- L'accès aux fichiers est contrôlé par l'authentification Laravel
- Les types de fichiers sont strictement validés
- La taille maximale est limitée à 200MB pour éviter les abus

## Monitoring

Surveillez :
- L'espace disque disponible
- L'utilisation de la mémoire pendant les uploads
- Les logs d'erreur PHP et du serveur web
- Les performances du serveur pendant les gros uploads
