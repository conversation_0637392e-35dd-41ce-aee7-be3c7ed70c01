<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateTemplateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $config = config('upload');

        return [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'sometimes|required|in:ppt,pptx,doc,docx,pdf',
            'file' => [
                'nullable',
                'file',
                function ($attribute, $value, $fail) use ($config) {
                    if (!$value) return; // Optional field for updates

                    if (!$value->isValid()) {
                        $fail('The file failed to upload.');
                        return;
                    }

                    $extension = strtolower($value->getClientOriginalExtension());
                    $mimeType = $value->getMimeType();
                    $size = $value->getSize();

                    // Determine file type and validate accordingly
                    if (in_array($extension, ['ppt', 'pptx'])) {
                        // PowerPoint validation
                        $allowedMimes = explode(',', $config['allowed_types']['powerpoint']['mimes']);
                        if (!in_array($mimeType, $allowedMimes) && !in_array($extension, ['ppt', 'pptx'])) {
                            $fail($config['validation_messages']['invalid_powerpoint']);
                            return;
                        }

                        $maxSize = $config['max_powerpoint_size'] * 1024; // Convert KB to bytes
                        if ($size > $maxSize) {
                            $fail($config['validation_messages']['powerpoint_size']);
                            return;
                        }
                    } elseif (in_array($extension, ['doc', 'docx', 'pdf'])) {
                        // Document validation
                        $allowedMimes = explode(',', $config['allowed_types']['documents']['mimes']);
                        if (!in_array($mimeType, $allowedMimes) && !in_array($extension, ['doc', 'docx', 'pdf'])) {
                            $fail($config['validation_messages']['invalid_document']);
                            return;
                        }

                        $maxSize = $config['max_document_size'] * 1024; // Convert KB to bytes
                        if ($size > $maxSize) {
                            $fail($config['validation_messages']['document_size']);
                            return;
                        }
                    } else {
                        $fail('Invalid file type. Only PowerPoint (.ppt, .pptx) and document files (.doc, .docx, .pdf) are allowed.');
                    }
                }
            ],
            'demo_video' => [
                'nullable',
                'file',
                function ($attribute, $value, $fail) use ($config) {
                    if (!$value) return; // Optional field

                    if (!$value->isValid()) {
                        $fail('The demo video failed to upload.');
                        return;
                    }

                    $extension = strtolower($value->getClientOriginalExtension());
                    $mimeType = $value->getMimeType();
                    $size = $value->getSize();

                    // Video validation
                    $allowedExtensions = $config['allowed_types']['videos']['extensions'];
                    if (!in_array($extension, $allowedExtensions)) {
                        $fail($config['validation_messages']['invalid_video']);
                        return;
                    }

                    $maxSize = $config['max_video_size'] * 1024; // Convert KB to bytes
                    if ($size > $maxSize) {
                        $fail($config['validation_messages']['video_size']);
                        return;
                    }
                }
            ],
            'is_active' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'file.mimes' => 'Template file must be a PowerPoint, Word, or PDF file.',
            'file.max' => 'Template file size must not exceed 200MB.',
            'demo_video.mimes' => 'Demo video must be an MP4, AVI, or MOV file.',
            'demo_video.max' => 'Demo video size must not exceed 200MB.',
            'type.in' => 'Template type must be ppt, docx, or pdf.',
        ];
    }
}

