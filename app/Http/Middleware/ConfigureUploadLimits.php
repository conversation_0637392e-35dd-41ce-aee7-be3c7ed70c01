<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ConfigureUploadLimits
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Set PHP configuration for large file uploads
        $this->configurePhpSettings();

        return $next($request);
    }

    /**
     * Configure PHP settings for large file uploads.
     */
    private function configurePhpSettings(): void
    {
        // Only set if we can modify ini settings
        if (function_exists('ini_set')) {
            // Get configuration values
            $config = config('upload.php_recommendations', [
                'upload_max_filesize' => '100M',
                'post_max_size' => '110M',
                'max_execution_time' => 600,
                'max_input_time' => 600,
                'memory_limit' => '512M',
                'max_file_uploads' => 20,
            ]);

            // Set upload limits based on configuration
            ini_set('upload_max_filesize', $config['upload_max_filesize']);
            ini_set('post_max_size', $config['post_max_size']);
            ini_set('max_file_uploads', (string) $config['max_file_uploads']);

            // Set execution time limits
            ini_set('max_execution_time', (string) $config['max_execution_time']);
            ini_set('max_input_time', (string) $config['max_input_time']);

            // Set memory limit if current limit is less than required
            $currentMemoryLimit = $this->convertToBytes(ini_get('memory_limit'));
            $requiredMemoryLimit = $this->convertToBytes($config['memory_limit']);

            if ($currentMemoryLimit < $requiredMemoryLimit) {
                ini_set('memory_limit', $config['memory_limit']);
            }

            // Log the configuration for debugging
            \Log::info('PHP upload limits configured', [
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
            ]);
        }
    }

    /**
     * Convert memory limit string to bytes.
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
                // no break
            case 'm':
                $value *= 1024;
                // no break
            case 'k':
                $value *= 1024;
        }

        return $value;
    }
}
