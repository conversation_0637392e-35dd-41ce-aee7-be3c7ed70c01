<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateTemplateRequest;
use App\Http\Requests\UpdateTemplateRequest;
use App\Http\Resources\TemplateResource;
use App\Models\Template;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class TemplateController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of templates
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Template::class);

        $query = Template::query();

        // Filter by type if provided
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by active status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $templates = $query->paginate(15);

        return TemplateResource::collection($templates);
    }

    /**
     * Store a newly created template (Admin only)
     */
    public function store(CreateTemplateRequest $request)
    {
        $this->authorize('create', Template::class);

        try {
            // Debug information
            \Log::info('Template creation attempt', [
                'has_file' => $request->hasFile('file'),
                'file_info' => $request->hasFile('file') ? [
                    'original_name' => $request->file('file')->getClientOriginalName(),
                    'size' => $request->file('file')->getSize(),
                    'mime_type' => $request->file('file')->getMimeType(),
                    'is_valid' => $request->file('file')->isValid(),
                    'error' => $request->file('file')->getError(),
                ] : null,
                'request_data' => $request->only(['name', 'description', 'type', 'is_active'])
            ]);

            $templateData = [
                'name' => $request->name,
                'description' => $request->description,
                'type' => $request->type,
                'is_active' => $request->boolean('is_active', true),
            ];

            // Handle file upload
            if ($request->hasFile('file')) {
                $file = $request->file('file');

                // Check if file is valid
                if (!$file->isValid()) {
                    $errorCode = $file->getError();
                    $errorMessages = [
                        UPLOAD_ERR_INI_SIZE => 'The uploaded file exceeds the server upload limit. Current limit: ' . ini_get('upload_max_filesize'),
                        UPLOAD_ERR_FORM_SIZE => 'The uploaded file exceeds the form size limit.',
                        UPLOAD_ERR_PARTIAL => 'The uploaded file was only partially uploaded. Please try again.',
                        UPLOAD_ERR_NO_FILE => 'No file was uploaded.',
                        UPLOAD_ERR_NO_TMP_DIR => 'Server configuration error: Missing temporary folder.',
                        UPLOAD_ERR_CANT_WRITE => 'Server configuration error: Failed to write file to disk.',
                        UPLOAD_ERR_EXTENSION => 'A server extension stopped the file upload.',
                    ];

                    $errorMessage = $errorMessages[$errorCode] ?? 'Unknown upload error.';

                    // Get file type for specific error messages
                    $extension = strtolower($file->getClientOriginalExtension());
                    $fileSize = $file->getSize();
                    $fileSizeMB = round($fileSize / (1024 * 1024), 2);

                    if ($errorCode === UPLOAD_ERR_INI_SIZE || $errorCode === UPLOAD_ERR_FORM_SIZE) {
                        if (in_array($extension, ['ppt', 'pptx'])) {
                            $errorMessage = "PowerPoint file too large ({$fileSizeMB}MB). Maximum allowed: 15MB. Consider optimizing images and removing unused elements.";
                        } elseif (in_array($extension, ['doc', 'docx', 'pdf'])) {
                            $errorMessage = "Document file too large ({$fileSizeMB}MB). Maximum allowed: 50MB.";
                        }
                    }

                    \Log::error('File upload error', [
                        'error_code' => $errorCode,
                        'error_message' => $errorMessage,
                        'file_name' => $file->getClientOriginalName(),
                        'file_size' => $fileSize,
                        'file_size_mb' => $fileSizeMB,
                        'file_extension' => $extension,
                        'mime_type' => $file->getMimeType(),
                        'php_upload_max_filesize' => ini_get('upload_max_filesize'),
                        'php_post_max_size' => ini_get('post_max_size'),
                        'php_memory_limit' => ini_get('memory_limit')
                    ]);

                    return response()->json([
                        'message' => 'The file failed to upload.',
                        'errors' => [
                            'file' => [$errorMessage]
                        ]
                    ], 422);
                }

                $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
                $filePath = $file->storeAs('templates/files', $filename, 'public');

                if (!$filePath) {
                    \Log::error('Failed to store file', [
                        'filename' => $filename,
                        'storage_disk' => 'public'
                    ]);

                    return response()->json([
                        'message' => 'The file failed to upload.',
                        'errors' => [
                            'file' => ['Failed to store file on disk.']
                        ]
                    ], 422);
                }

                $templateData['file_path'] = $filePath;
            }

            // Handle demo video upload
            if ($request->hasFile('demo_video')) {
                $video = $request->file('demo_video');

                if (!$video->isValid()) {
                    $errorCode = $video->getError();
                    $errorMessages = [
                        UPLOAD_ERR_INI_SIZE => 'The demo video exceeds the server upload limit. Current limit: ' . ini_get('upload_max_filesize'),
                        UPLOAD_ERR_FORM_SIZE => 'The demo video exceeds the form size limit.',
                        UPLOAD_ERR_PARTIAL => 'The demo video was only partially uploaded. Please try again.',
                        UPLOAD_ERR_NO_FILE => 'No video file was uploaded.',
                        UPLOAD_ERR_NO_TMP_DIR => 'Server configuration error: Missing temporary folder.',
                        UPLOAD_ERR_CANT_WRITE => 'Server configuration error: Failed to write video to disk.',
                        UPLOAD_ERR_EXTENSION => 'A server extension stopped the video upload.',
                    ];

                    $errorMessage = $errorMessages[$errorCode] ?? 'Unknown video upload error.';

                    $videoSize = $video->getSize();
                    $videoSizeMB = round($videoSize / (1024 * 1024), 2);

                    if ($errorCode === UPLOAD_ERR_INI_SIZE || $errorCode === UPLOAD_ERR_FORM_SIZE) {
                        $errorMessage = "Demo video too large ({$videoSizeMB}MB). Maximum allowed: 100MB. For 2-minute videos, consider using MP4 format with moderate compression.";
                    }

                    \Log::error('Video upload error', [
                        'error_code' => $errorCode,
                        'error_message' => $errorMessage,
                        'video_name' => $video->getClientOriginalName(),
                        'video_size' => $videoSize,
                        'video_size_mb' => $videoSizeMB,
                        'video_extension' => strtolower($video->getClientOriginalExtension()),
                        'mime_type' => $video->getMimeType(),
                    ]);

                    return response()->json([
                        'message' => 'The demo video failed to upload.',
                        'errors' => [
                            'demo_video' => [$errorMessage]
                        ]
                    ], 422);
                }

                $videoFilename = Str::uuid() . '.' . $video->getClientOriginalExtension();
                $videoPath = $video->storeAs('templates/videos', $videoFilename, 'public');

                if (!$videoPath) {
                    \Log::error('Failed to store video', [
                        'filename' => $videoFilename,
                        'storage_disk' => 'public'
                    ]);

                    return response()->json([
                        'message' => 'The demo video failed to upload.',
                        'errors' => [
                            'demo_video' => ['Failed to store video on disk.']
                        ]
                    ], 422);
                }

                $templateData['demo_video_path'] = $videoPath;
            }

            $template = Template::create($templateData);

            return response()->json([
                'message' => 'Template created successfully',
                'template' => new TemplateResource($template)
            ], 201);

        } catch (\Exception $e) {
            \Log::error('Template creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to create template',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified template
     */
    public function show(Template $template)
    {
        $this->authorize('view', $template);

        return new TemplateResource($template);
    }

    /**
     * Update the specified template (Admin only)
     */
    public function update(UpdateTemplateRequest $request, Template $template)
    {
        $this->authorize('update', $template);

        try {
            $updateData = $request->only(['name', 'description', 'type']);
            
            if ($request->has('is_active')) {
                $updateData['is_active'] = $request->boolean('is_active');
            }

            // Handle file upload
            if ($request->hasFile('file')) {
                // Delete old file
                if ($template->file_path) {
                    Storage::disk('public')->delete($template->file_path);
                }

                $file = $request->file('file');
                $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
                $filePath = $file->storeAs('templates/files', $filename, 'public');
                $updateData['file_path'] = $filePath;
            }

            // Handle demo video upload
            if ($request->hasFile('demo_video')) {
                // Delete old video
                if ($template->demo_video_path) {
                    Storage::disk('public')->delete($template->demo_video_path);
                }

                $video = $request->file('demo_video');
                $videoFilename = Str::uuid() . '.' . $video->getClientOriginalExtension();
                $videoPath = $video->storeAs('templates/videos', $videoFilename, 'public');
                $updateData['demo_video_path'] = $videoPath;
            }

            $template->update($updateData);

            return response()->json([
                'message' => 'Template updated successfully',
                'template' => new TemplateResource($template->fresh())
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update template',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified template (Admin only)
     */
    public function destroy(Template $template)
    {
        $this->authorize('delete', $template);

        try {
            // Delete associated files
            if ($template->file_path) {
                Storage::disk('public')->delete($template->file_path);
            }
            if ($template->demo_video_path) {
                Storage::disk('public')->delete($template->demo_video_path);
            }

            $template->delete();

            return response()->json([
                'message' => 'Template deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete template',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download template file (Admin and Collaborateur only)
     */
    public function download(Template $template)
    {
        $this->authorize('download', $template);

        if (!$template->file_path || !Storage::disk('public')->exists($template->file_path)) {
            return response()->json([
                'message' => 'Template file not found'
            ], 404);
        }

        $filePath = Storage::disk('public')->path($template->file_path);
        $fileName = $template->name . '.' . pathinfo($template->file_path, PATHINFO_EXTENSION);

        return response()->download($filePath, $fileName);
    }
}

