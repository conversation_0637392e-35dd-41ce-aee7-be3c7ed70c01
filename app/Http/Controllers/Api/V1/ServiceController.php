<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateServiceRequest;
use App\Http\Requests\UpdateServiceRequest;
use App\Http\Resources\ServiceResource;
use App\Models\Service;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of services
     */
    public function index(Request $request)
    {
        $query = Service::with('tools');

        // Filter by active status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $services = $query->paginate(15);

        return ServiceResource::collection($services);
    }

    /**
     * Store a newly created service (Admin only)
     */
    public function store(CreateServiceRequest $request)
    {
        $this->authorize('create', Service::class);

        try {
            $service = Service::create([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active', true),
            ]);

            return response()->json([
                'message' => 'Service created successfully',
                'service' => new ServiceResource($service)
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified service
     */
    public function show(Service $service)
    {
        return new ServiceResource($service->load('tools'));
    }

    /**
     * Update the specified service (Admin only)
     */
    public function update(UpdateServiceRequest $request, Service $service)
    {
        $this->authorize('update', $service);

        try {
            $service->update($request->validated());

            return response()->json([
                'message' => 'Service updated successfully',
                'service' => new ServiceResource($service->fresh()->load('tools'))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update service',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified service (Admin only)
     */
    public function destroy(Service $service)
    {
        $this->authorize('delete', $service);

        try {
            $service->delete();

            return response()->json([
                'message' => 'Service deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete service',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

