<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\VerifyCodeRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Models\VerificationCode;
use App\Services\WhatsAppService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AuthController extends Controller
{
    protected $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        $this->whatsAppService = $whatsAppService;
    }

    /**
     * Register a new user
     */
    public function register(RegisterRequest $request)
    {
        try {
            $user = User::create([
                'name' => $request->name,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'is_verified' => false,
            ]);

            // Assign default role
            $user->assignRole($request->role ?? 'user');

            // Send verification code
            $this->sendVerificationCode($request->phone);

            return response()->json([
                'message' => 'User registered successfully. Please verify your phone number.',
                'user' => new UserResource($user),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Registration failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login user
     */
    public function login(LoginRequest $request)
    {
        $credentials = $request->only('phone', 'password');

        if (!Auth::attempt($credentials)) {
            return response()->json([
                'message' => 'Invalid credentials'
            ], 401);
        }

        $user = Auth::user();

        if (!$user->is_verified) {
            return response()->json([
                'message' => 'Phone number not verified. Please verify your phone number first.',
                'requires_verification' => true
            ], 403);
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'message' => 'Login successful',
            'user' => new UserResource($user),
            'token' => $token,
            'token_type' => 'Bearer'
        ]);
    }

    /**
     * Send verification code
     */
    public function sendCode(Request $request)
    {
        $request->validate([
            'phone' => 'required|string'
        ]);

        try {
            // Check rate limiting (max 3 codes per hour)
            $recentCodes = VerificationCode::where('phone', $request->phone)
                ->where('created_at', '>=', Carbon::now()->subHour())
                ->count();

            if ($recentCodes >= 3) {
                return response()->json([
                    'message' => 'Too many verification attempts. Please try again later.'
                ], 429);
            }

            $this->sendVerificationCode($request->phone);

            return response()->json([
                'message' => 'Verification code sent successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to send verification code',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify phone number
     */
    public function verifyCode(VerifyCodeRequest $request)
    {
        $verificationCode = VerificationCode::where('phone', $request->phone)
            ->where('code', $request->code)
            ->where('is_used', false)
            ->first();

        if (!$verificationCode || $verificationCode->isExpired()) {
            return response()->json([
                'message' => 'Invalid or expired verification code'
            ], 400);
        }

        // Mark code as used
        $verificationCode->markAsUsed();

        // Update user verification status
        $user = User::where('phone', $request->phone)->first();
        if ($user) {
            $user->update([
                'is_verified' => true,
                'phone_verified_at' => Carbon::now()
            ]);

            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'message' => 'Phone verified successfully',
                'user' => new UserResource($user),
                'token' => $token,
                'token_type' => 'Bearer'
            ]);
        }

        return response()->json([
            'message' => 'User not found'
        ], 404);
    }

    /**
     * Get current user
     */
    public function me(Request $request)
    {
        return response()->json([
            'user' => new UserResource($request->user())
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Send verification code helper
     */
    private function sendVerificationCode($phone)
    {
        // Generate 6-digit code
        $code = str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);

        // Store in database
        VerificationCode::create([
            'phone' => $phone,
            'code' => $code,
            'expires_at' => Carbon::now()->addMinutes(10), // 10 minutes expiry
        ]);

        // Send via WhatsApp
        $this->whatsAppService->sendVerificationCode($phone, $code);
    }
}

