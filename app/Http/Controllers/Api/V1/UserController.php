<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Resources\UserResource;
use App\Models\User;
use App\Services\WhatsAppService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserController extends Controller
{
    protected $whatsAppService;

    public function __construct(WhatsAppService $whatsAppService)
    {
        $this->whatsAppService = $whatsAppService;
        $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of users (Admin only)
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', User::class);

        $users = User::with('roles')->paginate(15);

        return UserResource::collection($users);
    }

    /**
     * Store a newly created user (Admin only)
     */
    public function store(CreateUserRequest $request)
    {
        $this->authorize('create', User::class);

        try {
            $user = User::create([
                'name' => $request->name,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'is_verified' => $request->role === 'collaborateur' ? true : false,
            ]);

            // Assign role
            $user->assignRole($request->role);

            // If creating a collaborateur, send credentials via WhatsApp
            if ($request->role === 'collaborateur') {
                $this->whatsAppService->sendCollaborateurCredentials(
                    $request->phone,
                    $request->password
                );
            }

            return response()->json([
                'message' => 'User created successfully',
                'user' => new UserResource($user)
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified user (Admin only)
     */
    public function show(User $user)
    {
        $this->authorize('view', $user);

        return new UserResource($user->load('roles'));
    }

    /**
     * Update the specified user (Admin only)
     */
    public function update(UpdateUserRequest $request, User $user)
    {
        $this->authorize('update', $user);

        try {
            $updateData = $request->only(['name', 'phone']);

            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $user->update($updateData);

            // Update role if provided
            if ($request->filled('role')) {
                $user->syncRoles([$request->role]);
            }

            return response()->json([
                'message' => 'User updated successfully',
                'user' => new UserResource($user->fresh()->load('roles'))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified user (Admin only)
     */
    public function destroy(User $user)
    {
        $this->authorize('delete', $user);

        try {
            $user->delete();

            return response()->json([
                'message' => 'User deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * List collaborateurs (Admin only)
     */
    public function collaborateurs(Request $request)
    {
        $this->authorize('viewAny', User::class);

        $collaborateurs = User::role('collaborateur')->with('roles')->paginate(15);

        return UserResource::collection($collaborateurs);
    }

    /**
     * Create collaborateur with auto-generated password (Admin only)
     */
    public function createCollaborateur(CreateUserRequest $request)
    {
        $this->authorize('create', User::class);

        try {
            // Generate secure password
            $password = Str::random(12);

            $user = User::create([
                'name' => $request->name,
                'phone' => $request->phone,
                'password' => Hash::make($password),
                'is_verified' => true, // Auto-verified for collaborateurs
            ]);

            // Assign collaborateur role
            $user->assignRole('collaborateur');

            // Send credentials via WhatsApp
            $this->whatsAppService->sendCollaborateurCredentials(
                $request->phone,
                $password
            );

            return response()->json([
                'message' => 'Collaborateur created successfully. Credentials sent via WhatsApp.',
                'user' => new UserResource($user)
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create collaborateur',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

