<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateToolRequest;
use App\Http\Requests\UpdateToolRequest;
use App\Http\Resources\ToolResource;
use App\Models\Service;
use App\Models\Tool;
use Illuminate\Http\Request;

class ToolController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Display tools for a specific service
     */
    public function index(Service $service, Request $request)
    {
        $query = $service->tools();

        // Filter by active status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $tools = $query->paginate(15);

        return ToolResource::collection($tools);
    }

    /**
     * Store a newly created tool for a service (Admin only)
     */
    public function store(CreateToolRequest $request, Service $service)
    {
        $this->authorize('create', Tool::class);

        try {
            $tool = $service->tools()->create([
                'name' => $request->name,
                'description' => $request->description,
                'is_active' => $request->boolean('is_active', true),
            ]);

            return response()->json([
                'message' => 'Tool created successfully',
                'tool' => new ToolResource($tool->load('service'))
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified tool
     */
    public function show(Tool $tool)
    {
        return new ToolResource($tool->load('service'));
    }

    /**
     * Update the specified tool (Admin only)
     */
    public function update(UpdateToolRequest $request, Tool $tool)
    {
        $this->authorize('update', $tool);

        try {
            $tool->update($request->validated());

            return response()->json([
                'message' => 'Tool updated successfully',
                'tool' => new ToolResource($tool->fresh()->load('service'))
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified tool (Admin only)
     */
    public function destroy(Tool $tool)
    {
        $this->authorize('delete', $tool);

        try {
            $tool->delete();

            return response()->json([
                'message' => 'Tool deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete tool',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

