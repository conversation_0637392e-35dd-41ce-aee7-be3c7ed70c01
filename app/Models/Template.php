<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Template extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'file_path',
        'demo_video_path',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the file URL attribute.
     */
    public function getFileUrlAttribute()
    {
        return asset('storage/' . $this->file_path);
    }

    /**
     * Get the demo video URL attribute.
     */
    public function getDemoVideoUrlAttribute()
    {
        return $this->demo_video_path ? asset('storage/' . $this->demo_video_path) : null;
    }
}

