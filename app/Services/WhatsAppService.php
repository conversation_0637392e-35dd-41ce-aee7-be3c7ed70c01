<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class WhatsAppService
{
    protected $accountSid;
    protected $authToken;
    protected $fromNumber;

    public function __construct()
    {
        $this->accountSid = config('services.twilio.account_sid');
        $this->authToken = config('services.twilio.auth_token');
        $this->fromNumber = config('services.twilio.whatsapp_from');
    }

    /**
     * Send verification code via WhatsApp
     */
    public function sendVerificationCode($phone, $code)
    {
        $message = "Your SADEMY verification code is: {$code}. This code will expire in 10 minutes.";
        
        return $this->sendMessage($phone, $message);
    }

    /**
     * Send collaborateur credentials via WhatsApp
     */
    public function sendCollaborateurCredentials($phone, $password)
    {
        $message = "Welcome to SADEMY! Your login credentials:\nPhone: {$phone}\nPassword: {$password}\n\nPlease keep this information secure.";
        
        return $this->sendMessage($phone, $message);
    }

    /**
     * Send WhatsApp message using Twilio
     */
    protected function sendMessage($phone, $message)
    {
        try {
            // Format phone number for WhatsApp
            $toNumber = 'whatsapp:' . $this->formatPhoneNumber($phone);
            $fromNumber = 'whatsapp:' . $this->fromNumber;

            $response = Http::withBasicAuth($this->accountSid, $this->authToken)
                ->asForm()
                ->post("https://api.twilio.com/2010-04-01/Accounts/{$this->accountSid}/Messages.json", [
                    'From' => $fromNumber,
                    'To' => $toNumber,
                    'Body' => $message,
                ]);

            if ($response->successful()) {
                Log::info('WhatsApp message sent successfully', [
                    'phone' => $phone,
                    'message_sid' => $response->json('sid')
                ]);
                return true;
            } else {
                Log::error('Failed to send WhatsApp message', [
                    'phone' => $phone,
                    'error' => $response->json()
                ]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error('WhatsApp service error', [
                'phone' => $phone,
                'error' => $e->getMessage()
            ]);
            
            // For development/testing, log the message instead of actually sending
            if (config('app.env') === 'local') {
                Log::info('WhatsApp message (local development)', [
                    'phone' => $phone,
                    'message' => $message
                ]);
                return true;
            }
            
            return false;
        }
    }

    /**
     * Format phone number for international format
     */
    protected function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if not present (assuming +1 for US/Canada)
        if (!str_starts_with($phone, '1') && strlen($phone) === 10) {
            $phone = '1' . $phone;
        }
        
        return '+' . $phone;
    }
}

