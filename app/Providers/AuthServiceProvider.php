<?php

namespace App\Providers;

use App\Models\User;
use App\Models\Service;
use App\Models\Tool;
use App\Models\Template;
use App\Policies\UserPolicy;
use App\Policies\ServicePolicy;
use App\Policies\ToolPolicy;
use App\Policies\TemplatePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => UserPolicy::class,
        Service::class => ServicePolicy::class,
        Tool::class => ToolPolicy::class,
        Template::class => TemplatePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        //
    }
}
