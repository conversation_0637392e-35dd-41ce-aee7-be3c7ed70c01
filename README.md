# SADEMY API

A comprehensive Laravel REST API with authentication, role-based access control, WhatsApp verification, and file management capabilities.

## Table of Contents

- [Features](#features)
- [Requirements](#requirements)
- [Installation](#installation)
- [Configuration](#configuration)
- [Database Setup](#database-setup)
- [API Documentation](#api-documentation)
- [Authentication](#authentication)
- [Role-Based Access Control](#role-based-access-control)
- [WhatsApp Integration](#whatsapp-integration)
- [File Management](#file-management)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)

## Features

- **Authentication**: Phone-based authentication with Laravel Sanctum
- **Role-Based Access Control**: Admin, Collaborateur, and User roles with specific permissions
- **WhatsApp Verification**: Phone number verification via WhatsApp codes
- **File Management**: Secure upload and download of templates (PPT, DOCX, PDF) with demo videos
- **Service Management**: CRUD operations for services and their associated tools
- **Template Management**: Role-based access to template files and demo videos
- **API Versioning**: Structured API versioning (v1)
- **Rate Limiting**: Protection against abuse with throttling
- **Comprehensive Validation**: Form request validation for all endpoints
- **Resource Responses**: Consistent API responses using Laravel Resources

## Requirements

- PHP 8.1 or higher
- Composer
- MySQL 5.7 or higher
- Laravel 11.x
- Twilio Account (for WhatsApp integration)

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sademy-api
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Copy environment file**
   ```bash
   cp .env.example .env
   ```

4. **Generate application key**
   ```bash
   php artisan key:generate
   ```

5. **Configure your environment variables** (see [Configuration](#configuration))

6. **Run migrations and seeders**
   ```bash
   php artisan migrate --seed
   ```

7. **Create storage link**
   ```bash
   php artisan storage:link
   ```

8. **Start the development server**
   ```bash
   php artisan serve
   ```

## Configuration

### Database Configuration

Update your `.env` file with your database credentials:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sademy_api
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### Twilio WhatsApp Configuration

Configure your Twilio credentials for WhatsApp integration:

```env
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_FROM=+***********
```

### Application Configuration

```env
APP_NAME="SADEMY API"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000
```

## Database Setup

### Running Migrations

```bash
php artisan migrate
```

### Seeding the Database

The seeder will create default roles, permissions, and an admin user:

```bash
php artisan db:seed --class=RolePermissionSeeder
```

**Default Admin Credentials:**
- Phone: +**********
- Password: admin123

### Available Roles

- **Admin**: Full access to all features
- **Collaborateur**: Can view and download templates
- **User**: Can only view templates

## API Documentation

### Base URL

```
http://localhost:8000/api/v1
```

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
    "name": "John Doe",
    "phone": "+**********",
    "password": "password123",
    "password_confirmation": "password123",
    "role": "user"
}
```

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "phone": "+**********",
    "password": "password123"
}
```

#### Send Verification Code
```http
POST /api/v1/auth/send-code
Content-Type: application/json

{
    "phone": "+**********"
}
```

#### Verify Phone Number
```http
POST /api/v1/auth/verify-code
Content-Type: application/json

{
    "phone": "+**********",
    "code": "123456"
}
```

#### Get Current User
```http
GET /api/v1/auth/me
Authorization: Bearer {token}
```

#### Logout
```http
POST /api/v1/auth/logout
Authorization: Bearer {token}
```

### User Management (Admin Only)

#### List Users
```http
GET /api/v1/users
Authorization: Bearer {admin_token}
```

#### Create User
```http
POST /api/v1/users
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Jane Doe",
    "phone": "+1987654321",
    "password": "password123",
    "role": "collaborateur"
}
```

#### Update User
```http
PUT /api/v1/users/{id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Jane Smith",
    "phone": "+1987654321"
}
```

#### Delete User
```http
DELETE /api/v1/users/{id}
Authorization: Bearer {admin_token}
```

### Collaborateur Management (Admin Only)

#### List Collaborateurs
```http
GET /api/v1/collaborateurs
Authorization: Bearer {admin_token}
```

#### Create Collaborateur (Auto-verified with generated password)
```http
POST /api/v1/collaborateurs
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Collaborateur Name",
    "phone": "+1555123456"
}
```

### Service Management

#### List Services
```http
GET /api/v1/services
Authorization: Bearer {token}
```

#### Create Service (Admin Only)
```http
POST /api/v1/services
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Web Development",
    "description": "Professional web development services",
    "is_active": true
}
```

#### Get Service Details
```http
GET /api/v1/services/{id}
Authorization: Bearer {token}
```

#### Update Service (Admin Only)
```http
PUT /api/v1/services/{id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Updated Service Name",
    "description": "Updated description"
}
```

#### Delete Service (Admin Only)
```http
DELETE /api/v1/services/{id}
Authorization: Bearer {admin_token}
```

### Tool Management

#### List Tools for Service
```http
GET /api/v1/services/{service_id}/tools
Authorization: Bearer {token}
```

#### Add Tool to Service (Admin Only)
```http
POST /api/v1/services/{service_id}/tools
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Laravel Framework",
    "description": "PHP web application framework",
    "is_active": true
}
```

#### Update Tool (Admin Only)
```http
PUT /api/v1/tools/{id}
Authorization: Bearer {admin_token}
Content-Type: application/json

{
    "name": "Updated Tool Name",
    "description": "Updated description"
}
```

#### Delete Tool (Admin Only)
```http
DELETE /api/v1/tools/{id}
Authorization: Bearer {admin_token}
```

### Template Management

#### List Templates
```http
GET /api/v1/templates
Authorization: Bearer {token}
```

#### Get Template Details
```http
GET /api/v1/templates/{id}
Authorization: Bearer {token}
```

#### Upload Template (Admin Only)
```http
POST /api/v1/templates
Authorization: Bearer {admin_token}
Content-Type: multipart/form-data

{
    "name": "Business Presentation",
    "description": "Professional business presentation template",
    "type": "ppt",
    "file": [template_file],
    "demo_video": [video_file],
    "is_active": true
}
```

#### Update Template (Admin Only)
```http
PUT /api/v1/templates/{id}
Authorization: Bearer {admin_token}
Content-Type: multipart/form-data

{
    "name": "Updated Template Name",
    "description": "Updated description"
}
```

#### Download Template (Admin & Collaborateur Only)
```http
GET /api/v1/templates/{id}/download
Authorization: Bearer {token}
```

#### Delete Template (Admin Only)
```http
DELETE /api/v1/templates/{id}
Authorization: Bearer {admin_token}
```

## Authentication

The API uses Laravel Sanctum for token-based authentication. After successful login or phone verification, you'll receive a bearer token that must be included in the Authorization header for protected endpoints.

### Token Usage

```http
Authorization: Bearer {your_token_here}
```

### Token Expiration

Tokens don't expire by default but can be revoked by logging out.

## Role-Based Access Control

### Permissions by Role

| Feature | Admin | Collaborateur | User |
|---------|-------|---------------|------|
| Manage Users | ✅ | ❌ | ❌ |
| Manage Services | ✅ | ❌ | ❌ |
| Manage Tools | ✅ | ❌ | ❌ |
| Manage Templates | ✅ | ❌ | ❌ |
| View Templates | ✅ | ✅ | ✅ |
| Download Templates | ✅ | ✅ | ❌ |

### Template Access Rules

- **Admin**: Full CRUD operations + download
- **Collaborateur**: View details + download only
- **User**: View details only (no download)

## WhatsApp Integration

The API integrates with Twilio for WhatsApp messaging:

### Verification Flow

1. User registers with phone number
2. System sends 6-digit verification code via WhatsApp
3. User submits code for verification
4. Account becomes verified and active

### Collaborateur Creation

When an admin creates a collaborateur:
1. System generates secure random password
2. Sets `is_verified = true` automatically
3. Sends credentials via WhatsApp message
4. Collaborateur can login immediately

### Rate Limiting

- Maximum 3 verification codes per hour per phone number
- Codes expire after 10 minutes

## File Management

### Supported File Types

**Templates:**
- PowerPoint: .ppt, .pptx
- Word Documents: .doc, .docx
- PDF: .pdf

**Demo Videos:**
- MP4: .mp4
- AVI: .avi
- MOV: .mov

### File Size Limits

- Template files: 200MB maximum
- Demo videos: 200MB maximum

### Storage

Files are stored in `storage/app/public/templates/` with the following structure:
```
storage/app/public/templates/
├── files/          # Template files
└── videos/         # Demo videos
```

### Security

- Files are stored with UUID-based names to prevent guessing
- Access is controlled through authentication and authorization
- Download URLs are protected by role-based permissions

## Testing

### Running Tests

```bash
php artisan test
```

### Test Coverage

The API includes tests for:
- Authentication flows
- Role-based access control
- File upload and download
- API endpoint functionality
- Validation rules

## Deployment

### Production Environment

1. **Set environment to production**
   ```env
   APP_ENV=production
   APP_DEBUG=false
   ```

2. **Configure production database**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=your_production_host
   DB_DATABASE=your_production_database
   DB_USERNAME=your_production_username
   DB_PASSWORD=your_production_password
   ```

3. **Set up Twilio production credentials**
   ```env
   TWILIO_ACCOUNT_SID=your_production_sid
   TWILIO_AUTH_TOKEN=your_production_token
   TWILIO_WHATSAPP_FROM=your_production_number
   ```

4. **Optimize for production**
   ```bash
   composer install --optimize-autoloader --no-dev
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

5. **Set proper file permissions**
   ```bash
   chmod -R 755 storage bootstrap/cache
   ```

### Web Server Configuration

#### Nginx Configuration Example

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/sademy-api/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    index index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}
```

## Error Handling

### Standard Error Response Format

```json
{
    "message": "Error description",
    "error": "Detailed error information"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Authentication endpoints**: 60 requests per minute
- **General API endpoints**: 60 requests per minute
- **WhatsApp code sending**: 3 requests per hour per phone

## Security Features

- **CSRF Protection**: Disabled for API routes
- **CORS**: Configurable for cross-origin requests
- **Input Validation**: Comprehensive validation using Form Requests
- **SQL Injection Protection**: Eloquent ORM with parameter binding
- **File Upload Security**: Strict file type and size validation
- **Authentication**: Secure token-based authentication
- **Authorization**: Role-based access control with policies

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Coding Standards

- Follow PSR-12 coding standards
- Write comprehensive tests for new features
- Update documentation for API changes
- Use meaningful commit messages

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation for common solutions

## Changelog

### Version 1.0.0
- Initial release with full API functionality
- Phone-based authentication with WhatsApp verification
- Role-based access control (Admin, Collaborateur, User)
- Service and tool management
- Template management with file upload/download
- Comprehensive API documentation

# Sademy-api
